#!/usr/bin/env python3
"""
Script to view loss information from checkpoint files and training logs.
"""

import torch
import dill
import pathlib
import argparse
import pandas as pd
import matplotlib.pyplot as plt
import json
import os
from termcolor import cprint
from diffusion_policy_3d.common.json_logger import read_json_log

def load_checkpoint_info(ckpt_path):
    """Load checkpoint and extract basic info"""
    try:
        payload = torch.load(ckpt_path, pickle_module=dill, map_location='cpu')
        
        info = {
            'config': payload.get('cfg', {}),
            'epoch': None,
            'global_step': None,
            'train_loss': None,
            'test_mean_score': None
        }
        
        # Try to extract training info from state_dicts
        if 'state_dicts' in payload:
            for key, state_dict in payload['state_dicts'].items():
                if hasattr(state_dict, 'epoch'):
                    info['epoch'] = state_dict.epoch
                if hasattr(state_dict, 'global_step'):
                    info['global_step'] = state_dict.global_step
        
        return info
    except Exception as e:
        cprint(f"Error loading checkpoint {ckpt_path}: {e}", "red")
        return None

def find_training_logs(output_dir):
    """Find training log files in the output directory"""
    log_files = []
    output_path = pathlib.Path(output_dir)
    
    # Look for logs.json files
    for log_file in output_path.rglob("logs.json"):
        log_files.append(log_file)
    
    # Look for wandb logs
    for log_file in output_path.rglob("*.jsonl"):
        if "wandb" in str(log_file):
            log_files.append(log_file)
    
    return log_files

def read_training_logs(log_path):
    """Read training logs and extract loss information"""
    try:
        if log_path.suffix == '.json':
            # Read JSON logs
            df = read_json_log(str(log_path))
            return df
        else:
            # Try to read as JSON lines
            lines = []
            with open(log_path, 'r') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        lines.append(data)
                    except:
                        continue
            if lines:
                return pd.DataFrame(lines)
    except Exception as e:
        cprint(f"Error reading log {log_path}: {e}", "red")
    return None

def plot_loss_curves(df, save_path=None):
    """Plot loss curves from training logs"""
    if df is None or df.empty:
        cprint("No data to plot", "yellow")
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Training Progress', fontsize=16)
    
    # Plot 1: Training Loss
    if 'train_loss' in df.columns:
        axes[0, 0].plot(df.index, df['train_loss'], 'b-', alpha=0.7)
        axes[0, 0].set_title('Training Loss')
        axes[0, 0].set_xlabel('Step')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].grid(True)
    
    # Plot 2: Learning Rate
    if 'lr' in df.columns:
        axes[0, 1].plot(df.index, df['lr'], 'g-', alpha=0.7)
        axes[0, 1].set_title('Learning Rate')
        axes[0, 1].set_xlabel('Step')
        axes[0, 1].set_ylabel('LR')
        axes[0, 1].grid(True)
    
    # Plot 3: Test Score (if available)
    if 'test_mean_score' in df.columns:
        test_data = df.dropna(subset=['test_mean_score'])
        if not test_data.empty:
            axes[1, 0].plot(test_data.index, test_data['test_mean_score'], 'r-o', alpha=0.7)
            axes[1, 0].set_title('Test Mean Score')
            axes[1, 0].set_xlabel('Step')
            axes[1, 0].set_ylabel('Score')
            axes[1, 0].grid(True)
    
    # Plot 4: Epoch Progress
    if 'epoch' in df.columns:
        axes[1, 1].plot(df.index, df['epoch'], 'm-', alpha=0.7)
        axes[1, 1].set_title('Epoch Progress')
        axes[1, 1].set_xlabel('Step')
        axes[1, 1].set_ylabel('Epoch')
        axes[1, 1].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        cprint(f"Plot saved to {save_path}", "green")
    
    plt.show()

def main():
    parser = argparse.ArgumentParser(description='View checkpoint loss information')
    parser.add_argument('--checkpoint', type=str, help='Path to checkpoint file')
    parser.add_argument('--output_dir', type=str, help='Path to training output directory')
    parser.add_argument('--plot', action='store_true', help='Plot loss curves')
    parser.add_argument('--save_plot', type=str, help='Save plot to file')
    
    args = parser.parse_args()
    
    if args.checkpoint:
        # Load specific checkpoint
        ckpt_path = pathlib.Path(args.checkpoint)
        if not ckpt_path.exists():
            cprint(f"Checkpoint not found: {ckpt_path}", "red")
            return
        
        cprint(f"Loading checkpoint: {ckpt_path}", "cyan")
        info = load_checkpoint_info(ckpt_path)
        if info:
            cprint("Checkpoint Info:", "yellow")
            for key, value in info.items():
                if key != 'config' and value is not None:
                    cprint(f"  {key}: {value}", "white")
    
    if args.output_dir:
        # Find and read training logs
        output_path = pathlib.Path(args.output_dir)
        if not output_path.exists():
            cprint(f"Output directory not found: {output_path}", "red")
            return
        
        cprint(f"Searching for logs in: {output_path}", "cyan")
        
        # Look for logs.json
        log_file = output_path / "logs.json"
        if log_file.exists():
            cprint(f"Found training log: {log_file}", "green")
            df = read_training_logs(log_file)
            
            if df is not None and not df.empty:
                cprint(f"Log contains {len(df)} entries", "green")
                
                # Show basic statistics
                if 'train_loss' in df.columns:
                    train_loss = df['train_loss'].dropna()
                    if not train_loss.empty:
                        cprint(f"Training Loss - Min: {train_loss.min():.6f}, Max: {train_loss.max():.6f}, Latest: {train_loss.iloc[-1]:.6f}", "white")
                
                if 'test_mean_score' in df.columns:
                    test_score = df['test_mean_score'].dropna()
                    if not test_score.empty:
                        cprint(f"Test Score - Min: {test_score.min():.6f}, Max: {test_score.max():.6f}, Latest: {test_score.iloc[-1]:.6f}", "white")
                
                if 'epoch' in df.columns:
                    epochs = df['epoch'].dropna()
                    if not epochs.empty:
                        cprint(f"Epochs - Latest: {epochs.iloc[-1]}", "white")
                
                # Plot if requested
                if args.plot:
                    plot_loss_curves(df, args.save_plot)
                
                # Show last few entries
                cprint("\nLast 5 training entries:", "yellow")
                relevant_cols = ['epoch', 'global_step', 'train_loss', 'test_mean_score', 'lr']
                available_cols = [col for col in relevant_cols if col in df.columns]
                if available_cols:
                    print(df[available_cols].tail().to_string())
            else:
                cprint("No valid data found in logs", "yellow")
        else:
            cprint(f"No logs.json found in {output_path}", "yellow")
            
            # List available files
            cprint("Available files:", "cyan")
            for item in output_path.iterdir():
                if item.is_file():
                    cprint(f"  {item.name}", "white")

if __name__ == "__main__":
    main()
